#!/usr/bin/env python3
"""
Test script to verify that recipe edit functionality has been removed
while delete functionality remains intact.
"""

import requests
import json
import sys
import os

BASE_URL = "http://localhost:5000"

def test_edit_endpoint_removed():
    """Test that the PUT endpoint for editing recipes has been removed."""
    print("Testing that edit endpoint has been removed...")

    # Try to access the PUT endpoint without auth first
    try:
        response = requests.put(f"{BASE_URL}/api/recipe/test_id",
                              json={"name": "test"})

        # The route pattern /api/recipe/<recipe_id> exists for GET and DELETE
        # So PUT should return 405 Method Not Allowed, not 401/422
        if response.status_code == 405:
            print("✅ Edit endpoint properly removed (returns 405 Method Not Allowed)")
            return True
        elif response.status_code in [401, 422]:
            # This suggests Flask is processing the request through JWT middleware
            # which means there might be a PUT route registered somewhere
            print(f"⚠️  PUT request processed by auth middleware (status: {response.status_code})")
            print("    This could mean the route exists or <PERSON><PERSON><PERSON> is handling it differently")

            # Let's try with OPTIONS to see what methods are allowed
            options_response = requests.options(f"{BASE_URL}/api/recipe/test_id")
            if options_response.status_code == 200:
                allowed_methods = options_response.headers.get('Allow', '')
                print(f"    Allowed methods: {allowed_methods}")
                if 'PUT' in allowed_methods:
                    print("❌ PUT method is still listed as allowed")
                    return False
                else:
                    print("✅ PUT method not in allowed methods list")
                    return True
            else:
                print("    Could not determine allowed methods")
                return False
        else:
            print(f"❌ Edit endpoint still accessible (status: {response.status_code})")
            return False
    except requests.exceptions.ConnectionError:
        print("⚠️  Server not running - cannot test endpoints")
        return None

def test_delete_endpoint_exists():
    """Test that the DELETE endpoint for recipes still exists."""
    print("Testing that delete endpoint still exists...")

    try:
        # Test without auth first
        response = requests.delete(f"{BASE_URL}/api/recipe/test_id")

        # Should return 401 (Unauthorized) or 422 (JWT error), not 405 (Method Not Allowed)
        if response.status_code in [401, 422]:
            print("✅ Delete endpoint exists (returns auth error as expected)")
            return True
        elif response.status_code == 405:
            print("❌ Delete endpoint appears to be missing (Method Not Allowed)")
            return False
        else:
            print(f"✅ Delete endpoint exists (status: {response.status_code})")
            return True
    except requests.exceptions.ConnectionError:
        print("⚠️  Server not running - cannot test endpoints")
        return None

def check_community_html():
    """Check that edit button and related code has been removed from community.html."""
    print("Checking community.html for edit-related code...")
    
    try:
        with open('src/api/templates/community.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        issues = []
        
        # Check for edit button
        if 'btn-edit-recipe' in content:
            issues.append("Edit button CSS class still present")
        
        # Check for edit function
        if 'editRecipe(' in content:
            issues.append("editRecipe function still present")
        
        # Check for edit-related text
        if 'Edit Recipe' in content and 'btn-edit-recipe' in content:
            issues.append("Edit-related UI text still present")
        
        # Verify delete functionality is still there
        if 'deleteRecipe(' not in content:
            issues.append("Delete function appears to be missing")
        
        if 'btn-delete-recipe' not in content:
            issues.append("Delete button CSS class appears to be missing")
        
        if issues:
            print("❌ Issues found in community.html:")
            for issue in issues:
                print(f"   - {issue}")
            return False
        else:
            print("✅ community.html properly updated - edit removed, delete preserved")
            return True
            
    except FileNotFoundError:
        print("❌ community.html not found")
        return False

def check_routes_py():
    """Check that PUT route has been removed from routes.py."""
    print("Checking routes.py for edit endpoint...")
    
    try:
        with open('src/api/routes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        issues = []
        
        # Check for PUT route
        if "methods=['PUT']" in content and "update_shared_recipe" in content:
            issues.append("PUT route for updating recipes still present")
        
        # Verify DELETE route is still there
        if "methods=['DELETE']" not in content or "delete_shared_recipe" not in content:
            issues.append("DELETE route appears to be missing")
        
        if issues:
            print("❌ Issues found in routes.py:")
            for issue in issues:
                print(f"   - {issue}")
            return False
        else:
            print("✅ routes.py properly updated - PUT route removed, DELETE route preserved")
            return True
            
    except FileNotFoundError:
        print("❌ routes.py not found")
        return False

def check_shared_recipes_model():
    """Check that update function has been removed from shared_recipes.py."""
    print("Checking shared_recipes.py model...")
    
    try:
        with open('src/api/models/shared_recipes.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        issues = []
        
        # Check for update function
        if 'def update_shared_recipe(' in content:
            issues.append("update_shared_recipe function still present")
        
        # Verify delete function is still there
        if 'def delete_shared_recipe(' not in content:
            issues.append("delete_shared_recipe function appears to be missing")
        
        if issues:
            print("❌ Issues found in shared_recipes.py:")
            for issue in issues:
                print(f"   - {issue}")
            return False
        else:
            print("✅ shared_recipes.py properly updated - update function removed, delete function preserved")
            return True
            
    except FileNotFoundError:
        print("❌ shared_recipes.py not found")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing Recipe Edit Functionality Removal")
    print("=" * 50)
    
    results = []
    
    # File-based tests (always run)
    results.append(check_community_html())
    results.append(check_routes_py())
    results.append(check_shared_recipes_model())
    
    # Endpoint tests (only if server is running)
    edit_test = test_edit_endpoint_removed()
    delete_test = test_delete_endpoint_exists()
    
    if edit_test is not None:
        results.append(edit_test)
    if delete_test is not None:
        results.append(delete_test)
    
    print("\n" + "=" * 50)
    
    # Filter out None results
    valid_results = [r for r in results if r is not None]
    
    if all(valid_results):
        print("🎉 All tests passed! Edit functionality successfully removed.")
        print("✅ Delete functionality preserved and working.")
        return 0
    else:
        print("❌ Some tests failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
