#!/usr/bin/env python3
"""
Debug script to check Flask route registration
"""

import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.getcwd())

try:
    from src.api.app import app
    
    print("🔍 Debugging Flask Route Registration")
    print("=" * 50)
    
    # Get all routes
    routes = []
    for rule in app.url_map.iter_rules():
        routes.append({
            'endpoint': rule.endpoint,
            'methods': list(rule.methods),
            'rule': str(rule)
        })
    
    # Filter routes that match /api/recipe/<recipe_id>
    recipe_routes = [r for r in routes if '/api/recipe/<recipe_id>' in r['rule']]
    
    print(f"Found {len(recipe_routes)} routes matching '/api/recipe/<recipe_id>':")
    print()
    
    for route in recipe_routes:
        print(f"Rule: {route['rule']}")
        print(f"Endpoint: {route['endpoint']}")
        print(f"Methods: {route['methods']}")
        print("-" * 30)
    
    # Check if PUT is in any of the methods
    put_routes = [r for r in recipe_routes if 'PUT' in r['methods']]
    
    if put_routes:
        print("❌ Found routes with PUT method:")
        for route in put_routes:
            print(f"  - {route['rule']} -> {route['methods']}")
    else:
        print("✅ No routes with PUT method found")
    
    # Check DELETE routes
    delete_routes = [r for r in recipe_routes if 'DELETE' in r['methods']]
    
    if delete_routes:
        print("✅ Found routes with DELETE method:")
        for route in delete_routes:
            print(f"  - {route['rule']} -> {route['methods']}")
    else:
        print("❌ No routes with DELETE method found")
        
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
